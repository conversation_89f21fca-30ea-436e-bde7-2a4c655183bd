/* Import Local Fonts */
@import url('fonts.css');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --dark-bg: #0f172a;
    --darker-bg: #020617;
    --card-bg: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #334155;
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --nav-text-primary: #f8fafc;
    --nav-text-secondary: #cbd5e1;
}

/* Light Theme */
[data-theme="light"] {
    --primary-color: #4f46e5;
    --secondary-color: #7c3aed;
    --accent-color: #0891b2;
    --dark-bg: #ffffff;
    --darker-bg: #f8fafc;
    --card-bg: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --nav-text-primary: #ffffff;
    --nav-text-secondary: #e2e8f0;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

[data-theme="light"] .header {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(15, 23, 42, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-logo .pro {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--nav-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--nav-text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--nav-text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Navigation Controls */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.theme-toggle,
.search-toggle,
.help-toggle {
    background: none;
    border: none;
    color: var(--nav-text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover,
.search-toggle:hover,
.help-toggle:hover {
    color: var(--nav-text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.theme-icon {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

.search-toggle svg {
    width: 20px;
    height: 20px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
    animation: gradientShift 8s ease-in-out infinite;
}

/* Dynamic background gradient animation */
@keyframes gradientShift {
    0%, 100% {
        background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    }
    25% {
        background: radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
    }
    50% {
        background: radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    }
    75% {
        background: radial-gradient(circle at 20% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.15) 0%, transparent 50%);
    }
}

/* Particle background */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(2n) {
    background: rgba(139, 92, 246, 0.3);
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    background: rgba(6, 182, 212, 0.3);
    animation-delay: -4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

/* Mouse follower */
.mouse-follower {
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: screen;
}

.mouse-follower::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    min-height: 1.2em;
}

/* Typewriter effect */
.typewriter {
    overflow: hidden;
    border-right: 3px solid var(--primary-color);
    white-space: nowrap;
    display: inline-block;
    animation: blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-color); }
}

.hero-badge {
    display: inline-block;
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.badge-text {
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.75rem 1rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-highlight:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.feature-icon-small {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
}

.feature-icon-small svg {
    width: 100%;
    height: 100%;
}

.feature-highlight span {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Live converter preview */
.live-converter-preview {
    background: rgba(30, 41, 59, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 20px;
    padding: 2.5rem;
    margin: 3rem auto;
    max-width: 550px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 30px rgba(99, 102, 241, 0.2);
    position: relative;
    overflow: hidden;
}

.live-converter-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    pointer-events: none;
}

/* Light theme adjustments for live converter */
[data-theme="light"] .live-converter-preview {
    background: rgba(248, 250, 252, 0.9);
    border: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1),
                0 0 30px rgba(99, 102, 241, 0.1);
}

[data-theme="light"] .live-converter-input {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.3);
    color: var(--text-primary);
}

[data-theme="light"] .live-converter-input:focus {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
}

[data-theme="light"] .live-converter-result {
    background: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.3);
    color: #0891b2;
}

.live-converter-title {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    position: relative;
    z-index: 1;
}

.live-converter-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
    /* flex-wrap: wrap; */
    justify-content: center;
}

.live-converter-input {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.5);
    border-radius: 8px;
    padding: 0.875rem 1rem;
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    flex: 1;
    max-width: 150px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.live-converter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background: rgba(15, 23, 42, 1);
}

/* Tooltip for exact values */
.live-converter-result[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.live-converter-result[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}

.live-converter-result {
    position: relative;
}

.live-converter-unit {
    background: var(--gradient-primary);
    color: white;
    padding: 0.875rem 1.25rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    min-width: 85px;
    text-align: center;
    white-space: nowrap;
}

.live-converter-arrow {
    font-size: 1.25rem;
    color: var(--primary-color);
    font-weight: bold;
    margin: 0 0.25rem;
}

.live-converter-result {
    background: rgba(6, 182, 212, 0.15);
    border: 1px solid rgba(6, 182, 212, 0.4);
    border-radius: 8px;
    padding: 0.875rem 1rem;
    color: #22d3ee;
    font-weight: 700;
    font-size: 1.125rem;
    text-align: center;
    min-width: 120px;
    flex: 1;
    max-width: 150px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.hero-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 60px;
    height: 60px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: var(--dark-bg);
    border-top: 1px solid var(--border-color);
}

.features-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.features-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item-inline {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature-check {
    width: 24px;
    height: 24px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.feature-item-inline span {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

.features-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.conversion-preview {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.conversion-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.05;
    pointer-events: none;
}

.preview-card {
    position: relative;
    z-index: 1;
}

.preview-header {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
}

.preview-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}

.preview-input,
.preview-output {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.preview-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.preview-unit {
    font-size: 0.875rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.preview-arrow {
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: bold;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 3000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
}

.search-container {
    width: 90%;
    max-width: 600px;
    background: var(--card-bg);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: searchSlideIn 0.3s ease;
}

@keyframes searchSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.search-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    gap: 1rem;
}

.search-icon {
    width: 20px;
    height: 20px;
    color: var(--text-muted);
    flex-shrink: 0;
}

.search-box input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.125rem;
    outline: none;
}

.search-box input::placeholder {
    color: var(--text-muted);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.search-close:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.search-close svg {
    width: 16px;
    height: 16px;
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-color);
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.search-result-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

.search-result-content {
    flex: 1;
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.search-result-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.search-result-shortcut {
    font-size: 0.75rem;
    color: var(--text-muted);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: monospace;
}

.search-shortcuts {
    padding: 1rem;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.shortcut-hint {
    font-size: 0.75rem;
    color: var(--text-muted);
}

kbd {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    font-family: monospace;
    color: var(--text-primary);
}

/* Tools Section */
.tools-section {
    padding: 5rem 0;
    background: var(--darker-bg);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Tools Filter */
.tools-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.filter-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tool-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.tool-card:hover::before {
    opacity: 0.05;
}

.tool-card:hover {
    transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 30px rgba(99, 102, 241, 0.3);
    border-color: var(--primary-color);
    perspective: 1000px;
}

.tool-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.tool-icon svg {
    width: 30px;
    height: 30px;
    color: white;
}

.tool-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    position: relative;
    z-index: 1;
}

.tool-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
    margin-bottom: 1rem;
}

.tool-shortcut {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: var(--text-muted);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: monospace;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.tool-card:hover .tool-shortcut {
    opacity: 1;
}

.tool-card.hidden {
    display: none;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close {
    color: var(--text-muted);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 1.1rem;
}

.converter {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.input-row {
    display: flex;
    gap: 1rem;
}

.input-row input,
.input-row select {
    flex: 1;
    padding: 0.75rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-row input:focus,
.input-row select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.swap-button {
    display: flex;
    justify-content: center;
}

.swap-button button {
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.swap-button button:hover {
    transform: rotate(180deg);
    box-shadow: var(--shadow-md);
}

/* Validation and Error Styles */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.3s ease;
}

.error-icon {
    font-size: 1.25rem;
}

.error-text {
    color: #fca5a5;
    font-size: 0.875rem;
    font-weight: 500;
}

.input-validation {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    min-height: 1rem;
}

.validation-error {
    color: #fca5a5;
}

.validation-success {
    color: #86efac;
}

.conversion-info {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
    min-height: 1rem;
}

.conversion-details {
    margin-top: 1.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    animation: slideIn 0.3s ease;
}

.details-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.details-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 500;
}

.detail-value {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Input States */
.input-row input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-row input.success {
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.input-row input:invalid {
    border-color: #ef4444;
}

.input-row input:valid:not(:placeholder-shown) {
    border-color: #22c55e;
}

/* Conversion Actions */
.conversion-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
    justify-content: center;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--text-primary);
    background: rgba(99, 102, 241, 0.1);
}

.action-btn svg {
    width: 16px;
    height: 16px;
}

.action-btn.success {
    border-color: #22c55e;
    color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

/* History Modal */
.history-modal {
    max-width: 700px;
}

.history-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-secondary {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-empty {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
}

.history-empty svg {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.history-empty p {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.history-item:last-child {
    border-bottom: none;
}

.history-conversion {
    flex: 1;
}

.history-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.history-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.history-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.history-actions {
    display: flex;
    gap: 0.5rem;
}

.history-action {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.history-action:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.history-action svg {
    width: 16px;
    height: 16px;
}

/* Keyboard Shortcuts Help */
.shortcuts-help {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-xl);
    z-index: 2000;
    max-width: 300px;
    display: none;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.shortcuts-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.shortcut-item span {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.shortcuts-close {
    width: 100%;
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.shortcuts-close:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Footer */
.footer {
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
    align-items: start;
}

.footer-brand {
    max-width: none;
}

.footer-links {
    justify-self: end;
}

.footer-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social-icons {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    background: #64748b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.social-icon svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
    z-index: 2;
    position: relative;
}

/* Specific social media colors */
.social-icon.facebook:hover {
    background: #1877f2;
}

.social-icon.twitter:hover {
    background: #1da1f2;
}

.social-icon.discord:hover {
    background: #5865f2;
}

.social-icon.dribbble:hover {
    background: #ea4c89;
}

.social-icon.link:hover {
    background: var(--primary-color);
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-section h3 .pro {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Additional Page Styles */
.main-content {
    padding-top: 100px;
    min-height: calc(100vh - 200px);
}

.page-header {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.page-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.page-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.last-updated {
    font-size: 0.875rem;
    color: var(--text-muted);
    position: relative;
    z-index: 1;
}

.content-grid {
    max-width: 800px;
    margin: 0 auto;
    padding: 3rem 0;
}

.content-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
}

.content-section h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: var(--text-primary);
}

.content-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
    color: var(--text-primary);
}

.content-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.content-section ul {
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.content-section li {
    margin-bottom: 0.5rem;
}

.features-list {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon svg {
    width: 24px;
    height: 24px;
    color: white;
}

.feature-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.feature-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.category-item {
    padding: 1.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.category-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.category-item h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.category-item p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.contact-info {
    background: var(--dark-bg);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin: 1rem 0;
}

.contact-info p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::after {
    width: 100%;
}

.nav-logo a {
    text-decoration: none;
}

/* Medium screens */
@media (max-width: 1024px) {
    .live-converter-preview {
        max-width: 90%;
        padding: 2rem;
    }

    .live-converter-row {
        gap: 0.5rem;
    }

    .live-converter-input,
    .live-converter-result {
        min-width: 100px;
        max-width: 130px;
    }

    .live-converter-unit {
        min-width: 70px;
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Prevent horizontal overflow */
    body {
        overflow-x: hidden;
        max-width: 100vw;
    }

    * {
        max-width: 100%;
        box-sizing: border-box;
    }
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--card-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: 2rem 0;
    }

    .nav-menu .nav-link {
        color: var(--text-secondary);
    }

    .nav-menu .nav-link:hover {
        color: var(--text-primary);
    }

    .nav-menu .nav-link.active {
        color: var(--primary-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .input-row {
        flex-direction: column;
    }

    .content-section {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .content-grid {
        padding: 2rem 0;
    }

    /* Hero section mobile optimizations */
    .hero {
        min-height: 100vh;
        padding: 2rem 0;
        padding-top: calc(70px + 3rem); /* Header height + extra spacing */
        margin-top: 0;
    }

    .hero-container {
        padding: 0 1rem;
        max-width: 100%;
        overflow-x: hidden;
    }

    .hero-content {
        text-align: center;
        max-width: 100%;
        overflow-x: hidden;
    }

    .hero-title {
        font-size: clamp(1.75rem, 6vw, 2.5rem);
        line-height: 1.2;
        margin-bottom: 1rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }

    .hero-subtitle {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
        padding: 0 0.5rem;
        line-height: 1.5;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 100%;
    }

    .hero-badge {
        margin-top: 1rem;
        margin-bottom: 1rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.875rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .feature-highlight {
        padding: 0.6rem 1rem;
        font-size: 0.875rem;
    }

    .hero-stats {
        margin-top: 2rem;
    }

    .features-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .features-visual {
        order: -1;
    }

    .conversion-preview {
        padding: 1.5rem;
    }

    .preview-content {
        flex-direction: column;
        gap: 1.5rem;
    }

    .preview-arrow {
        transform: rotate(90deg);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-links {
        justify-self: center;
    }

    .footer-features {
        justify-content: center;
    }

    .footer-social-icons {
        justify-content: center;
    }

    .floating-element {
        display: none;
    }

    /* Live converter responsive */
    .live-converter-preview {
        padding: 1.5rem 1rem;
        margin: 1.5rem auto;
        max-width: calc(100% - 2rem);
        border-radius: 16px;
    }

    .live-converter-title {
        font-size: 1.1rem;
        margin-bottom: 1.25rem;
    }

    .live-converter-row {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .live-converter-input,
    .live-converter-result {
        max-width: none;
        width: 100%;
        text-align: center;
        padding: 0.75rem;
        font-size: 1rem;
    }

    .live-converter-unit {
        min-width: auto;
        width: 100%;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .live-converter-arrow {
        transform: rotate(90deg);
        margin: 0.25rem 0;
        font-size: 1.5rem;
        align-self: center;
    }

    .nav-controls {
        gap: 0.25rem;
    }

    /* Additional mobile optimizations */
    .hero-animation {
        display: none; /* Hide complex animations on mobile for better performance */
    }

    .particles-container {
        display: none; /* Hide particles on mobile */
    }

    .mouse-follower {
        display: none !important; /* Hide mouse follower on mobile */
    }

    /* Improve touch targets */
    .feature-highlight {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Better spacing for mobile */
    .hero-stats {
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .search-overlay {
        padding-top: 5vh;
    }

    .search-container {
        width: 95%;
    }

    .tools-filter {
        gap: 0.5rem;
    }

    .filter-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .conversion-actions {
        flex-wrap: wrap;
    }

    .action-btn {
        flex: 1;
        min-width: 0;
        justify-content: center;
    }

    .shortcuts-help {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
