<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navigation Test - Smartnery</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Test styles to make mobile nav visible */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
        }
        
        .test-content {
            padding: 2rem;
            margin-top: 80px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
        }
        
        /* Force mobile view for testing */
        @media (min-width: 769px) {
            .mobile-menu-toggle {
                display: flex !important;
            }
            
            .nav-menu {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header with new mobile navigation -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>DataConvert<span class="pro">Pro</span></h1>
                </div>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link">Home</a></li>
                    <li><a href="#about" class="nav-link">About</a></li>
                    <li><a href="#privacy" class="nav-link">Privacy</a></li>
                    <li><a href="#terms" class="nav-link">Terms</a></li>
                </ul>
                
                <div class="nav-controls">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                        <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75z"/>
                        </svg>
                        <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9"/>
                        </svg>
                    </button>
                    <button class="search-toggle mobile-search-toggle" id="searchToggle" title="Search tools">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
                        </svg>
                    </button>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle mobile menu" aria-expanded="false">
                    <div class="hamburger-icon">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </div>
                </button>
                
                <!-- Mobile Navigation Overlay -->
                <div class="mobile-nav-overlay" id="mobileNavOverlay">
                    <div class="mobile-nav-content">
                        <div class="mobile-nav-header">
                            <div class="mobile-nav-logo">
                                <h2>DataConvert<span class="pro">Pro</span></h2>
                            </div>
                            <button class="mobile-nav-close" id="mobileNavClose" aria-label="Close mobile menu">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        
                        <nav class="mobile-nav-menu">
                            <a href="#home" class="mobile-nav-link">
                                <div class="mobile-nav-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                    </svg>
                                </div>
                                <span>Home</span>
                            </a>
                            <a href="#about" class="mobile-nav-link">
                                <div class="mobile-nav-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                                    </svg>
                                </div>
                                <span>About</span>
                            </a>
                            <a href="#privacy" class="mobile-nav-link">
                                <div class="mobile-nav-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2z"/>
                                    </svg>
                                </div>
                                <span>Privacy</span>
                            </a>
                            <a href="#terms" class="mobile-nav-link">
                                <div class="mobile-nav-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6z"/>
                                    </svg>
                                </div>
                                <span>Terms</span>
                            </a>
                        </nav>
                        
                        <div class="mobile-nav-actions">
                            <button class="mobile-action-btn" id="mobileSearchBtn">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
                                </svg>
                                <span>Search Tools</span>
                            </button>
                            <button class="mobile-action-btn" id="mobileThemeBtn">
                                <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75z"/>
                                </svg>
                                <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                    <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9"/>
                                </svg>
                                <span>Toggle Theme</span>
                            </button>
                        </div>
                        
                        <div class="mobile-nav-footer">
                            <p>Professional unit conversion tools</p>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Test Content -->
    <div class="test-content">
        <div class="test-section">
            <h2>Mobile Navigation Test</h2>
            <p>This page is designed to test the new mobile navigation functionality.</p>
            <button class="test-button" onclick="testMobileNav()">Test Mobile Nav Toggle</button>
            <button class="test-button" onclick="testThemeToggle()">Test Theme Toggle</button>
        </div>
        
        <div class="test-section" id="home">
            <h3>Home Section</h3>
            <p>This is the home section content.</p>
        </div>
        
        <div class="test-section" id="about">
            <h3>About Section</h3>
            <p>This is the about section content.</p>
        </div>
        
        <div class="test-section" id="privacy">
            <h3>Privacy Section</h3>
            <p>This is the privacy section content.</p>
        </div>
        
        <div class="test-section" id="terms">
            <h3>Terms Section</h3>
            <p>This is the terms section content.</p>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
    <script>
        function testMobileNav() {
            const toggle = document.getElementById('mobileMenuToggle');
            if (toggle) {
                toggle.click();
            }
        }
        
        function testThemeToggle() {
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.click();
            }
        }
        
        // Log mobile nav events for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile nav test page loaded');
            
            const mobileToggle = document.getElementById('mobileMenuToggle');
            const mobileOverlay = document.getElementById('mobileNavOverlay');
            
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function() {
                    console.log('Mobile nav toggle clicked');
                });
            }
            
            if (mobileOverlay) {
                mobileOverlay.addEventListener('transitionend', function() {
                    console.log('Mobile nav transition completed');
                });
            }
        });
    </script>
</body>
</html>
