# Smartnery

A modern, professional unit conversion website with a sleek tech-inspired design. Built with pure HTML, CSS, and JavaScript - no external dependencies required.

## Features

### 🔧 Conversion Tools
- **Currency Exchange** - Real-time currency conversion for major world currencies
- **Weight Units** - Comprehensive weight and mass conversions
- **Volume Units** - Liquid and dry volume measurements
- **Time Units** - Precise time conversions from nanoseconds to years
- **Storage Data** - Digital storage units from bits to petabytes
- **Speed Units** - Speed conversions including specialized units
- **Pressure Units** - Scientific and industrial pressure measurements
- **Power Units** - Electrical and mechanical power conversions
- **Length Units** - Linear measurements across all scales
- **Force Units** - Mechanical force units for engineering
- **Density Units** - Mass per unit volume calculations
- **Area Units** - Surface area measurements

### 🎨 Design Features
- Modern, tech-inspired UI with gradient backgrounds and floating animations
- Responsive design that works perfectly on all devices
- Dark theme with professional color scheme and visual hierarchy
- Smooth animations, hover effects, and micro-interactions
- Interactive modal-based conversion interface with real-time feedback
- Clean typography using locally hosted Inter font family
- Rich content sections with feature highlights and conversion previews

### 🔒 Privacy & Performance
- All conversions performed locally in browser with zero data transmission
- No registration, tracking, or data storage
- Fast, lightweight, and fully offline-capable after initial load
- Local font files and resources for optimal performance
- Privacy-focused design with transparent data handling

### ✅ Advanced Validation
- Real-time input validation with visual feedback
- Comprehensive error handling and user guidance
- Input sanitization and range checking
- Scientific notation support for extreme values
- Detailed conversion information and formulas
- Professional-grade precision controls

## Project Structure

```
tools1/
├── index.html              # Main homepage
├── about.html              # About page
├── privacy.html            # Privacy policy
├── terms.html              # Terms of service
├── README.md               # This file
└── assets/
    ├── css/
    │   ├── style.css       # Main stylesheet
    │   └── fonts.css       # Font definitions
    ├── js/
    │   └── script.js       # Main JavaScript functionality
    ├── fonts/
    │   ├── inter-regular.woff2
    │   ├── inter-medium.woff2
    │   └── inter-bold.woff2
    └── images/
        └── favicon.svg     # Site favicon
```

## Getting Started

### Local Development
1. Clone or download the project files
2. Open a terminal in the project directory
3. Start a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
4. Open your browser to `http://localhost:8000`

### Deployment
Simply upload all files to your web server. No build process or server-side configuration required.

## Browser Support
- Chrome/Chromium 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Technical Details

### Technologies Used
- **HTML5** - Semantic markup and modern features
- **CSS3** - Grid, Flexbox, custom properties, animations
- **JavaScript ES6+** - Modern syntax and features
- **Web Fonts** - Local Inter font family files

### Key Features
- **Responsive Design** - Mobile-first approach with breakpoints
- **Accessibility** - Semantic HTML and keyboard navigation
- **Performance** - Optimized assets and minimal dependencies
- **SEO Friendly** - Proper meta tags and structured content

### Conversion Accuracy
- Uses internationally accepted conversion factors
- Handles edge cases and precision arithmetic
- Supports scientific notation for very large/small numbers
- Currency rates are sample data (not real-time)

## Customization

### Colors
Edit CSS custom properties in `assets/css/style.css`:
```css
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    /* ... more variables */
}
```

### Adding New Conversion Types
1. Add conversion data to `conversionData` object in `script.js`
2. Add a new tool card to the HTML
3. Update the modal handling logic

### Fonts
Replace font files in `assets/fonts/` and update `assets/css/fonts.css`

## License
This project is open source and available under the MIT License.

## Contributing
Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## Support
For questions or support, please open an issue in the project repository.

---

**Smartnery** - Professional unit conversion tools for precise calculations.
