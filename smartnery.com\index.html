<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smartnery - Advanced Unit Conversion Tools</title>
    <meta name="description" content="Professional unit conversion tools for currency, weight, volume, time, storage, speed, pressure, power, length, force, density, and area conversions.">

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="unit converter, currency exchange, weight conversion, volume conversion, time conversion, storage conversion, speed conversion, pressure conversion, power conversion, length conversion, force conversion, density conversion, area conversion">
    <meta name="author" content="Smartnery">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Smartnery - Advanced Unit Conversion Tools">
    <meta property="og:description" content="Professional-grade unit conversion tools with 12+ categories and 100+ units. Fast, accurate, and privacy-focused.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://smartnery.com">
    <meta property="og:image" content="assets/images/favicon.svg">
    <meta property="og:site_name" content="Smartnery">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Smartnery - Advanced Unit Conversion Tools">
    <meta name="twitter:description" content="Professional-grade unit conversion tools with 12+ categories and 100+ units. Fast, accurate, and privacy-focused.">
    <meta name="twitter:image" content="assets/images/favicon.svg">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#6366f1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Smartnery">
    <meta name="msapplication-TileColor" content="#6366f1">


    <!-- Preload Critical Resources -->
    <link rel="preload" href="assets/css/style.css" as="style">
    <link rel="preload" href="assets/js/script.js" as="script">
    <link rel="preload" href="assets/fonts/inter-regular.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Icons and Manifest -->
    <link rel="icon" type="image/svg+xml" href="assets/images/favicon.svg">
    <link rel="manifest" href="manifest.json">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Smartnery",
      "description": "Professional unit conversion tools for currency, weight, volume, time, storage, speed, pressure, power, length, force, density, and area conversions.",
      "url": "https://smartnery.com",
      "applicationCategory": "UtilitiesApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Currency Exchange",
        "Weight Unit Conversion",
        "Volume Unit Conversion",
        "Time Unit Conversion",
        "Storage Data Conversion",
        "Speed Unit Conversion",
        "Pressure Unit Conversion",
        "Power Unit Conversion",
        "Length Unit Conversion",
        "Force Unit Conversion",
        "Density Unit Conversion",
        "Area Unit Conversion"
      ]
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>DataConvert<span class="pro">Pro</span></h1>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">Home</a></li>
                    <li><a href="about.html" class="nav-link">About</a></li>
                    <li><a href="privacy.html" class="nav-link">Privacy</a></li>
                    <li><a href="terms.html" class="nav-link">Terms</a></li>
                </ul>
                <div class="nav-controls">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme (Ctrl+T)">
                        <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"/>
                        </svg>
                        <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"/>
                        </svg>
                    </button>
                    <button class="search-toggle" id="searchToggle" title="Search tools (Ctrl+K)">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
                        </svg>
                    </button>
                    <button class="help-toggle" id="helpToggle" title="Help & Tutorial">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.879 7.519c0-1.316 1.066-2.385 2.381-2.385s2.381 1.069 2.381 2.385c0 1.316-1.066 2.385-2.381 2.385s-2.381-1.069-2.381-2.385zM15.271 12.888c-.317-.785-.891-1.077-1.734-1.077-1.052 0-1.734.292-1.734 1.077 0 .785.682 1.077 1.734 1.077.843 0 1.417-.292 1.734-1.077z"/>
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                        </svg>
                    </button>
                </div>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <!-- Particle Background -->
        <div class="particles-container" id="particlesContainer"></div>

        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span class="badge-text">Professional Grade</span>
                </div>
                <h1 class="hero-title typewriter" id="heroTitle">Advanced Unit Conversion Tools<span class="typewriter">|</span></h1>
                <p class="hero-subtitle">Precision conversion utilities designed for professionals, students, and researchers who demand accuracy</p>
                <div class="hero-features">
                    <div class="feature-highlight">
                        <div class="feature-icon-small">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <span>Instant Results</span>
                    </div>
                    <div class="feature-highlight">
                        <div class="feature-icon-small">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2z"/>
                            </svg>
                        </div>
                        <span>Privacy Protected</span>
                    </div>
                    <div class="feature-highlight">
                        <div class="feature-icon-small">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <span>No Registration</span>
                    </div>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number" data-target="12">0</span>
                        <span class="stat-label">Conversion Categories</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" data-target="100">0</span>
                        <span class="stat-label">Unit Options</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" data-target="24">0</span>
                        <span class="stat-label">Available 24/7</span>
                    </div>
                </div>

                <!-- Live Converter Preview -->
                <div class="live-converter-preview">
                    <h3 class="live-converter-title">Try it now - Length Converter</h3>
                    <div class="live-converter-row">
                        <input type="number" class="live-converter-input" id="liveInput" placeholder="100" value="100">
                        <div class="live-converter-unit">meters</div>
                        <div class="live-converter-arrow">→</div>
                        <div class="live-converter-result" id="liveResult">328.084</div>
                        <div class="live-converter-unit">feet</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-animation">
            <div class="floating-element element-1"></div>
            <div class="floating-element element-2"></div>
            <div class="floating-element element-3"></div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="features-content">
                <div class="features-text">
                    <h2 class="features-title">Why Choose Smartnery?</h2>
                    <p class="features-description">Built for professionals who need reliable, accurate conversions without compromise</p>
                    <div class="features-list">
                        <div class="feature-item-inline">
                            <div class="feature-check">✓</div>
                            <span>High-precision calculations with scientific notation support</span>
                        </div>
                        <div class="feature-item-inline">
                            <div class="feature-check">✓</div>
                            <span>Real-time conversion as you type</span>
                        </div>
                        <div class="feature-item-inline">
                            <div class="feature-check">✓</div>
                            <span>Works offline - no internet required after loading</span>
                        </div>
                        <div class="feature-item-inline">
                            <div class="feature-check">✓</div>
                            <span>Mobile-optimized responsive design</span>
                        </div>
                    </div>
                </div>
                <div class="features-visual">
                    <div class="conversion-preview">
                        <div class="preview-card">
                            <div class="preview-header">Length Conversion</div>
                            <div class="preview-content">
                                <div class="preview-input">
                                    <span class="preview-value">100</span>
                                    <span class="preview-unit">meters</span>
                                </div>
                                <div class="preview-arrow">→</div>
                                <div class="preview-output">
                                    <span class="preview-value">328.084</span>
                                    <span class="preview-unit">feet</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-container">
            <div class="search-box">
                <svg class="search-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
                </svg>
                <input type="text" id="searchInput" placeholder="Search conversion tools..." autocomplete="off">
                <button class="search-close" id="searchClose">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="search-results" id="searchResults"></div>
            <div class="search-shortcuts">
                <span class="shortcut-hint">Press <kbd>Esc</kbd> to close</span>
            </div>
        </div>
    </div>

    <!-- Conversion Tools Grid -->
    <section class="tools-section">
        <div class="container">
            <h2 class="section-title">Conversion Tools</h2>
            <p class="section-subtitle">Select any tool below to start converting units instantly</p>
            <div class="tools-filter">
                <button class="filter-btn active" data-filter="all">All Tools</button>
                <button class="filter-btn" data-filter="measurement">Measurement</button>
                <button class="filter-btn" data-filter="digital">Digital</button>
                <button class="filter-btn" data-filter="physics">Physics</button>
                <button class="filter-btn" data-filter="finance">Finance</button>
            </div>
            <div class="tools-grid">
                <!-- Currency Exchange -->
                <div class="tool-card" data-tool="currency" data-category="finance" data-keywords="currency money exchange rate dollar euro">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Currency Exchange</h3>
                    <p class="tool-description">Real-time currency conversion</p>
                    <div class="tool-shortcut">Ctrl+1</div>
                </div>

                <!-- Weight Units -->
                <div class="tool-card" data-tool="weight" data-category="measurement" data-keywords="weight mass kilogram pound gram">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3l4 4h-3v10h-2V7H8l4-4z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Weight Units</h3>
                    <p class="tool-description">Weight unit conversions</p>
                    <div class="tool-shortcut">Ctrl+2</div>
                </div>

                <!-- Volume Units -->
                <div class="tool-card" data-tool="volume" data-category="measurement" data-keywords="volume liter gallon milliliter">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Volume Units</h3>
                    <p class="tool-description">Volume unit conversions</p>
                    <div class="tool-shortcut">Ctrl+3</div>
                </div>

                <!-- Time Units -->
                <div class="tool-card" data-tool="time" data-category="measurement" data-keywords="time second minute hour day">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm4.2 14.2L11 13V7h1.5v5.2l4.5 2.7-.8 1.3z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Time Units</h3>
                    <p class="tool-description">Time unit conversions</p>
                    <div class="tool-shortcut">Ctrl+4</div>
                </div>

                <!-- Storage Data -->
                <div class="tool-card" data-tool="storage" data-category="digital" data-keywords="storage data byte gigabyte terabyte">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Storage Data</h3>
                    <p class="tool-description">Data storage unit conversions</p>
                    <div class="tool-shortcut">Ctrl+5</div>
                </div>

                <!-- Speed Units -->
                <div class="tool-card" data-tool="speed" data-category="physics" data-keywords="speed velocity mph kmh">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.38 8.57l-1.23 1.85a8 8 0 0 1-.22 7.58H5.07A8 8 0 0 1 15.58 6.85l1.85-1.23A10 10 0 0 0 3.35 19a2 2 0 0 0 1.72 1h13.85a2 2 0 0 0 1.74-1 10 10 0 0 0-.27-10.44z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Speed Units</h3>
                    <p class="tool-description">Speed unit conversions</p>
                    <div class="tool-shortcut">Ctrl+6</div>
                </div>

                <!-- Pressure Units -->
                <div class="tool-card" data-tool="pressure" data-category="physics" data-keywords="pressure pascal bar psi">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Pressure Units</h3>
                    <p class="tool-description">Pressure unit conversions</p>
                    <div class="tool-shortcut">Ctrl+7</div>
                </div>

                <!-- Power Units -->
                <div class="tool-card" data-tool="power" data-category="physics" data-keywords="power watt horsepower energy">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.69 2.21L4.33 11.49c-.64.58-.28 1.65.58 1.73L13 14l-4.85 6.76c-.22.31-.19.74.08 ********.77.31 1.08.02L19.67 12.51c.64-.58.28-1.65-.58-1.73L11 10l4.85-6.76c.22-.31.19-.74-.08-1.01-.3-.3-.77-.31-1.08-.02z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Power Units</h3>
                    <p class="tool-description">Power unit conversions</p>
                    <div class="tool-shortcut">Ctrl+8</div>
                </div>

                <!-- Length Units -->
                <div class="tool-card" data-tool="length" data-category="measurement" data-keywords="length distance meter foot inch">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17h18v2H3zm0-6h18v2H3zm0-6h18v2H3z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Length Units</h3>
                    <p class="tool-description">Length unit conversions</p>
                    <div class="tool-shortcut">Ctrl+9</div>
                </div>

                <!-- Force Units -->
                <div class="tool-card" data-tool="force" data-category="physics" data-keywords="force newton pound dyne">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Force Units</h3>
                    <p class="tool-description">Force unit conversions</p>
                    <div class="tool-shortcut">Ctrl+0</div>
                </div>

                <!-- Density Units -->
                <div class="tool-card" data-tool="density" data-category="physics" data-keywords="density mass volume">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Density Units</h3>
                    <p class="tool-description">Density unit conversions</p>
                    <div class="tool-shortcut">Ctrl+Shift+1</div>
                </div>

                <!-- Area Units -->
                <div class="tool-card" data-tool="area" data-category="measurement" data-keywords="area square meter acre hectare">
                    <div class="tool-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/>
                        </svg>
                    </div>
                    <h3 class="tool-title">Area Units</h3>
                    <p class="tool-description">Area unit conversions</p>
                    <div class="tool-shortcut">Ctrl+Shift+2</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Conversion Modal -->
    <div class="modal" id="conversionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">Unit Converter</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="converter">
                    <div class="error-message" id="errorMessage" style="display: none;">
                        <div class="error-icon">⚠️</div>
                        <span class="error-text"></span>
                    </div>
                    <div class="input-group">
                        <label for="fromValue">From:</label>
                        <div class="input-row">
                            <input type="number" id="fromValue" placeholder="Enter value" step="any">
                            <select id="fromUnit"></select>
                        </div>
                        <div class="input-validation" id="fromValidation"></div>
                    </div>
                    <div class="swap-button">
                        <button id="swapUnits" title="Swap units">⇄</button>
                    </div>
                    <div class="input-group">
                        <label for="toValue">To:</label>
                        <div class="input-row">
                            <input type="number" id="toValue" placeholder="Result" readonly>
                            <select id="toUnit"></select>
                        </div>
                        <div class="conversion-info" id="conversionInfo"></div>
                    </div>
                    <div class="conversion-details" id="conversionDetails" style="display: none;">
                        <div class="details-header">Conversion Details</div>
                        <div class="details-content">
                            <div class="detail-item">
                                <span class="detail-label">Formula:</span>
                                <span class="detail-value" id="formulaDisplay"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Precision:</span>
                                <span class="detail-value" id="precisionDisplay"></span>
                            </div>
                        </div>
                    </div>
                    <div class="conversion-actions">
                        <button class="action-btn" id="saveToHistory" title="Save to history">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                            </svg>
                            Save
                        </button>
                        <button class="action-btn" id="copyResult" title="Copy result">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                            </svg>
                            Copy
                        </button>
                        <button class="action-btn" id="viewHistory" title="View history">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9z"/>
                            </svg>
                            History
                        </button>
                        <button class="action-btn" id="showComparison" title="Compare multiple units">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                            </svg>
                            Compare
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal" id="historyModal">
        <div class="modal-content history-modal">
            <div class="modal-header">
                <h2 class="modal-title">Conversion History</h2>
                <div class="history-controls">
                    <button class="btn-secondary" id="clearHistory">Clear All</button>
                    <span class="close" id="historyClose">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <div class="history-list" id="historyList">
                    <div class="history-empty">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9z"/>
                        </svg>
                        <p>No conversion history yet</p>
                        <span>Your recent conversions will appear here</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Modal -->
    <div class="modal" id="comparisonModal">
        <div class="modal-content comparison-modal">
            <div class="modal-header">
                <h2 class="modal-title" id="comparisonTitle">Unit Comparison</h2>
                <span class="close" id="comparisonClose">&times;</span>
            </div>
            <div class="modal-body">
                <div class="comparison-input">
                    <label for="comparisonValue">Value to compare:</label>
                    <input type="number" id="comparisonValue" placeholder="Enter value" step="any">
                </div>
                <div class="comparison-results" id="comparisonResults">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Keyboard Shortcuts Help -->
    <div class="shortcuts-help" id="shortcutsHelp">
        <div class="shortcuts-content">
            <h3>Keyboard Shortcuts</h3>
            <div class="shortcuts-list">
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>K</kbd>
                    <span>Search tools</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>T</kbd>
                    <span>Toggle theme</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>1-0</kbd>
                    <span>Open conversion tool</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Esc</kbd>
                    <span>Close modal/search</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Tab</kbd>
                    <span>Swap units (in converter)</span>
                </div>
            </div>
            <button class="shortcuts-close" id="shortcutsClose">Got it!</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-brand">
                    <h3>DataConvert<span class="pro">Pro</span></h3>
                    <p>Professional unit conversion tools designed for accuracy and reliability. All conversions are performed locally in your browser for maximum privacy and speed.</p>
                    <div class="footer-social-icons">
                        <a href="https://facebook.com" class="social-icon facebook" target="_blank" rel="noopener noreferrer" title="Follow us on Facebook">
                            <svg viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="https://twitter.com" class="social-icon twitter" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter">
                            <svg viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="https://discord.com" class="social-icon discord" target="_blank" rel="noopener noreferrer" title="Join our Discord">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z"/>
                            </svg>
                        </a>
                        <a href="https://dribbble.com" class="social-icon dribbble" target="_blank" rel="noopener noreferrer" title="Follow us on Dribbble">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-icon link" title="Share this page">
                            <svg viewBox="0 0 24 24">
                                <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div class="footer-section footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Smartnery. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Mouse Follower -->
    <div class="mouse-follower" id="mouseFollower"></div>

    <script src="assets/js/script.js"></script>
</body>
</html>
