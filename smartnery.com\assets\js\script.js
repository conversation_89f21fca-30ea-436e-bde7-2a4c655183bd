// Unit conversion data and functions
const conversionData = {
    currency: {
        title: 'Currency Exchange',
        units: {
            'USD': { name: 'US Dollar', rate: 1 },
            'EUR': { name: 'Euro', rate: 0.85 },
            'GBP': { name: 'British Pound', rate: 0.73 },
            'JPY': { name: 'Japanese Yen', rate: 110 },
            'CNY': { name: 'Chinese Yuan', rate: 6.45 },
            'CAD': { name: 'Canadian Dollar', rate: 1.25 },
            'AUD': { name: 'Australian Dollar', rate: 1.35 },
            'CHF': { name: 'Swiss Franc', rate: 0.92 },
            'INR': { name: 'Indian Rupee', rate: 74.5 },
            'KRW': { name: 'South Korean Won', rate: 1180 }
        }
    },
    weight: {
        title: 'Weight Units',
        units: {
            'kg': { name: 'Kilogram', factor: 1 },
            'g': { name: 'Gram', factor: 0.001 },
            'mg': { name: 'Milligram', factor: 0.000001 },
            'lb': { name: 'Pound', factor: 0.453592 },
            'oz': { name: 'Ounce', factor: 0.0283495 },
            'ton': { name: 'Metric Ton', factor: 1000 },
            'stone': { name: 'Stone', factor: 6.35029 },
            'grain': { name: 'Grain', factor: 0.0000647989 }
        }
    },
    volume: {
        title: 'Volume Units',
        units: {
            'l': { name: 'Liter', factor: 1 },
            'ml': { name: 'Milliliter', factor: 0.001 },
            'gal_us': { name: 'US Gallon', factor: 3.78541 },
            'gal_uk': { name: 'UK Gallon', factor: 4.54609 },
            'qt': { name: 'Quart', factor: 0.946353 },
            'pt': { name: 'Pint', factor: 0.473176 },
            'cup': { name: 'Cup', factor: 0.236588 },
            'fl_oz': { name: 'Fluid Ounce', factor: 0.0295735 },
            'm3': { name: 'Cubic Meter', factor: 1000 },
            'cm3': { name: 'Cubic Centimeter', factor: 0.001 }
        }
    },
    time: {
        title: 'Time Units',
        units: {
            's': { name: 'Second', factor: 1 },
            'min': { name: 'Minute', factor: 60 },
            'h': { name: 'Hour', factor: 3600 },
            'day': { name: 'Day', factor: 86400 },
            'week': { name: 'Week', factor: 604800 },
            'month': { name: 'Month', factor: 2629746 },
            'year': { name: 'Year', factor: 31556952 },
            'ms': { name: 'Millisecond', factor: 0.001 },
            'μs': { name: 'Microsecond', factor: 0.000001 },
            'ns': { name: 'Nanosecond', factor: 0.000000001 }
        }
    },
    storage: {
        title: 'Storage Data Units',
        units: {
            'B': { name: 'Byte', factor: 1 },
            'KB': { name: 'Kilobyte', factor: 1024 },
            'MB': { name: 'Megabyte', factor: 1048576 },
            'GB': { name: 'Gigabyte', factor: 1073741824 },
            'TB': { name: 'Terabyte', factor: 1099511627776 },
            'PB': { name: 'Petabyte', factor: 1125899906842624 },
            'bit': { name: 'Bit', factor: 0.125 },
            'Kbit': { name: 'Kilobit', factor: 128 },
            'Mbit': { name: 'Megabit', factor: 131072 },
            'Gbit': { name: 'Gigabit', factor: 134217728 }
        }
    },
    speed: {
        title: 'Speed Units',
        units: {
            'mps': { name: 'Meters per Second', factor: 1 },
            'kph': { name: 'Kilometers per Hour', factor: 0.277778 },
            'mph': { name: 'Miles per Hour', factor: 0.44704 },
            'fps': { name: 'Feet per Second', factor: 0.3048 },
            'knot': { name: 'Knot', factor: 0.514444 },
            'mach': { name: 'Mach', factor: 343 },
            'c': { name: 'Speed of Light', factor: 299792458 }
        }
    },
    pressure: {
        title: 'Pressure Units',
        units: {
            'Pa': { name: 'Pascal', factor: 1 },
            'kPa': { name: 'Kilopascal', factor: 1000 },
            'MPa': { name: 'Megapascal', factor: 1000000 },
            'bar': { name: 'Bar', factor: 100000 },
            'atm': { name: 'Atmosphere', factor: 101325 },
            'psi': { name: 'PSI', factor: 6894.76 },
            'torr': { name: 'Torr', factor: 133.322 },
            'mmHg': { name: 'mmHg', factor: 133.322 }
        }
    },
    power: {
        title: 'Power Units',
        units: {
            'W': { name: 'Watt', factor: 1 },
            'kW': { name: 'Kilowatt', factor: 1000 },
            'MW': { name: 'Megawatt', factor: 1000000 },
            'hp': { name: 'Horsepower', factor: 745.7 },
            'BTU/h': { name: 'BTU per Hour', factor: 0.293071 },
            'cal/s': { name: 'Calorie per Second', factor: 4.184 },
            'ft⋅lbf/s': { name: 'Foot-pound per Second', factor: 1.35582 }
        }
    },
    length: {
        title: 'Length Units',
        units: {
            'm': { name: 'Meter', factor: 1 },
            'km': { name: 'Kilometer', factor: 1000 },
            'cm': { name: 'Centimeter', factor: 0.01 },
            'mm': { name: 'Millimeter', factor: 0.001 },
            'in': { name: 'Inch', factor: 0.0254 },
            'ft': { name: 'Foot', factor: 0.3048 },
            'yd': { name: 'Yard', factor: 0.9144 },
            'mi': { name: 'Mile', factor: 1609.34 },
            'nm': { name: 'Nautical Mile', factor: 1852 },
            'μm': { name: 'Micrometer', factor: 0.000001 }
        }
    },
    force: {
        title: 'Force Units',
        units: {
            'N': { name: 'Newton', factor: 1 },
            'kN': { name: 'Kilonewton', factor: 1000 },
            'dyne': { name: 'Dyne', factor: 0.00001 },
            'lbf': { name: 'Pound-force', factor: 4.44822 },
            'kgf': { name: 'Kilogram-force', factor: 9.80665 },
            'gf': { name: 'Gram-force', factor: 0.00980665 },
            'pdl': { name: 'Poundal', factor: 0.138255 }
        }
    },
    density: {
        title: 'Density Units',
        units: {
            'kg/m3': { name: 'Kilogram per Cubic Meter', factor: 1 },
            'g/cm3': { name: 'Gram per Cubic Centimeter', factor: 1000 },
            'g/ml': { name: 'Gram per Milliliter', factor: 1000 },
            'lb/ft3': { name: 'Pound per Cubic Foot', factor: 16.0185 },
            'lb/in3': { name: 'Pound per Cubic Inch', factor: 27679.9 },
            'oz/gal': { name: 'Ounce per Gallon', factor: 7.48915 },
            'kg/l': { name: 'Kilogram per Liter', factor: 1000 }
        }
    },
    area: {
        title: 'Area Units',
        units: {
            'm2': { name: 'Square Meter', factor: 1 },
            'km2': { name: 'Square Kilometer', factor: 1000000 },
            'cm2': { name: 'Square Centimeter', factor: 0.0001 },
            'mm2': { name: 'Square Millimeter', factor: 0.000001 },
            'in2': { name: 'Square Inch', factor: 0.00064516 },
            'ft2': { name: 'Square Foot', factor: 0.092903 },
            'yd2': { name: 'Square Yard', factor: 0.836127 },
            'acre': { name: 'Acre', factor: 4046.86 },
            'hectare': { name: 'Hectare', factor: 10000 },
            'mi2': { name: 'Square Mile', factor: 2589988.11 }
        }
    }
};

// DOM elements
const modal = document.getElementById('conversionModal');
const modalTitle = document.getElementById('modalTitle');
const fromValue = document.getElementById('fromValue');
const toValue = document.getElementById('toValue');
const fromUnit = document.getElementById('fromUnit');
const toUnit = document.getElementById('toUnit');
const swapButton = document.getElementById('swapUnits');
const closeButton = document.querySelector('.close');
// Mobile navigation elements
const mobileMenuToggle = document.getElementById('mobileMenuToggle');
const mobileNavOverlay = document.getElementById('mobileNavOverlay');
const mobileNavClose = document.getElementById('mobileNavClose');
const mobileSearchBtn = document.getElementById('mobileSearchBtn');
const mobileThemeBtn = document.getElementById('mobileThemeBtn');
const errorMessage = document.getElementById('errorMessage');
const fromValidation = document.getElementById('fromValidation');
const conversionInfo = document.getElementById('conversionInfo');
const conversionDetails = document.getElementById('conversionDetails');
const formulaDisplay = document.getElementById('formulaDisplay');
const precisionDisplay = document.getElementById('precisionDisplay');

// New feature elements
const themeToggle = document.getElementById('themeToggle');
const searchToggle = document.getElementById('searchToggle');
const searchOverlay = document.getElementById('searchOverlay');
const searchInput = document.getElementById('searchInput');
const searchClose = document.getElementById('searchClose');
const searchResults = document.getElementById('searchResults');
const historyModal = document.getElementById('historyModal');
const historyClose = document.getElementById('historyClose');
const historyList = document.getElementById('historyList');
const clearHistory = document.getElementById('clearHistory');
const saveToHistory = document.getElementById('saveToHistory');
const copyResult = document.getElementById('copyResult');
const viewHistory = document.getElementById('viewHistory');
const shortcutsHelp = document.getElementById('shortcutsHelp');
const shortcutsClose = document.getElementById('shortcutsClose');

// Current conversion type
let currentTool = '';

// Validation rules
const validationRules = {
    currency: { min: 0, max: 1e15, decimals: 2 },
    weight: { min: 0, max: 1e15, decimals: 10 },
    volume: { min: 0, max: 1e15, decimals: 10 },
    time: { min: 0, max: 1e15, decimals: 10 },
    storage: { min: 0, max: 1e20, decimals: 0 },
    speed: { min: 0, max: 1e10, decimals: 10 },
    pressure: { min: 0, max: 1e15, decimals: 10 },
    power: { min: 0, max: 1e15, decimals: 10 },
    length: { min: 0, max: 1e15, decimals: 10 },
    force: { min: 0, max: 1e15, decimals: 10 },
    density: { min: 0, max: 1e10, decimals: 10 },
    area: { min: 0, max: 1e15, decimals: 10 }
};

// Tool metadata for search and filtering
const toolMetadata = {
    currency: { category: 'finance', keywords: ['currency', 'money', 'exchange', 'rate', 'dollar', 'euro'], shortcut: 'Ctrl+1' },
    weight: { category: 'measurement', keywords: ['weight', 'mass', 'kilogram', 'pound', 'gram'], shortcut: 'Ctrl+2' },
    volume: { category: 'measurement', keywords: ['volume', 'liter', 'gallon', 'milliliter'], shortcut: 'Ctrl+3' },
    time: { category: 'measurement', keywords: ['time', 'second', 'minute', 'hour', 'day'], shortcut: 'Ctrl+4' },
    storage: { category: 'digital', keywords: ['storage', 'data', 'byte', 'gigabyte', 'terabyte'], shortcut: 'Ctrl+5' },
    speed: { category: 'physics', keywords: ['speed', 'velocity', 'mph', 'kmh'], shortcut: 'Ctrl+6' },
    pressure: { category: 'physics', keywords: ['pressure', 'pascal', 'bar', 'psi'], shortcut: 'Ctrl+7' },
    power: { category: 'physics', keywords: ['power', 'watt', 'horsepower', 'energy'], shortcut: 'Ctrl+8' },
    length: { category: 'measurement', keywords: ['length', 'distance', 'meter', 'foot', 'inch'], shortcut: 'Ctrl+9' },
    force: { category: 'physics', keywords: ['force', 'newton', 'pound', 'dyne'], shortcut: 'Ctrl+0' },
    density: { category: 'physics', keywords: ['density', 'mass', 'volume'], shortcut: 'Ctrl+Shift+1' },
    area: { category: 'measurement', keywords: ['area', 'square', 'meter', 'acre', 'hectare'], shortcut: 'Ctrl+Shift+2' }
};

// State management
let conversionHistory = JSON.parse(localStorage.getItem('conversionHistory') || '[]');
let currentTheme = localStorage.getItem('theme') || 'dark';
let isFirstVisit = !localStorage.getItem('hasVisited');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeEventListeners();
    initializeAnimations();
    initializeKeyboardShortcuts();
    initializeAccessibility();
    initializePWA();

    // New enhanced features
    initializeParticles();
    initializeTypewriter();
    initializeCounters();
    initializeLiveConverter();
    initializeMouseFollower();

    // Show shortcuts help for first-time visitors
    if (isFirstVisit) {
        setTimeout(() => {
            showShortcutsHelp();
            localStorage.setItem('hasVisited', 'true');
        }, 2000);
    }
});

// Theme management
function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();
}

function toggleTheme() {
    currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    updateThemeIcon();
}

function updateThemeIcon() {
    const sunIcon = document.querySelector('.sun-icon');
    const moonIcon = document.querySelector('.moon-icon');

    if (currentTheme === 'dark') {
        sunIcon.style.display = 'block';
        moonIcon.style.display = 'none';
    } else {
        sunIcon.style.display = 'none';
        moonIcon.style.display = 'block';
    }
}

function initializeEventListeners() {
    // Theme toggle
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Search functionality
    if (searchToggle) {
        searchToggle.addEventListener('click', openSearch);
    }
    if (searchClose) {
        searchClose.addEventListener('click', closeSearch);
    }
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    // History functionality
    if (viewHistory) {
        viewHistory.addEventListener('click', openHistoryModal);
    }
    if (historyClose) {
        historyClose.addEventListener('click', closeHistoryModal);
    }
    if (clearHistory) {
        clearHistory.addEventListener('click', clearConversionHistory);
    }
    if (saveToHistory) {
        saveToHistory.addEventListener('click', saveCurrentConversion);
    }
    if (copyResult) {
        copyResult.addEventListener('click', copyConversionResult);
    }

    // Shortcuts help
    if (shortcutsClose) {
        shortcutsClose.addEventListener('click', hideShortcutsHelp);
    }

    // Tool filtering
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            filterTools(filter);

            // Update active state
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Tool card clicks
    document.querySelectorAll('.tool-card').forEach(card => {
        card.addEventListener('click', function() {
            const tool = this.getAttribute('data-tool');
            openConverter(tool);
        });
    });

    // Footer tool links
    document.querySelectorAll('[data-tool]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tool = this.getAttribute('data-tool');
            openConverter(tool);
        });
    });

    // Modal close
    if (closeButton) {
        closeButton.addEventListener('click', closeModal);
    }
    if (modal) {
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }

    // Conversion inputs with debouncing
    if (fromValue) {
        let conversionTimeout;
        fromValue.addEventListener('input', function() {
            clearTimeout(conversionTimeout);
            conversionTimeout = setTimeout(performConversion, 300);
        });

        // Real-time validation on blur
        fromValue.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                performConversion();
            }
        });

        // Prevent invalid characters
        fromValue.addEventListener('keypress', function(e) {
            const char = String.fromCharCode(e.which);
            const value = this.value;

            // Allow numbers, decimal point, minus sign, and control keys
            if (!/[\d\.\-eE]/.test(char) && e.which !== 8 && e.which !== 0) {
                e.preventDefault();
                return false;
            }

            // Prevent multiple decimal points
            if (char === '.' && value.includes('.')) {
                e.preventDefault();
                return false;
            }

            // Prevent multiple minus signs or minus not at start
            if (char === '-' && (value.includes('-') || this.selectionStart !== 0)) {
                e.preventDefault();
                return false;
            }
        });
    }

    if (fromUnit) {
        fromUnit.addEventListener('change', performConversion);
    }
    if (toUnit) {
        toUnit.addEventListener('change', performConversion);
    }

    // Swap units
    if (swapButton) {
        swapButton.addEventListener('click', swapUnits);
    }

    // Mobile navigation functionality
    function openMobileNav() {
        if (mobileNavOverlay && mobileMenuToggle) {
            mobileNavOverlay.classList.add('active');
            mobileMenuToggle.classList.add('active');
            document.body.classList.add('mobile-nav-open');
            mobileMenuToggle.setAttribute('aria-expanded', 'true');
        }
    }

    function closeMobileNav() {
        if (mobileNavOverlay && mobileMenuToggle) {
            mobileNavOverlay.classList.remove('active');
            mobileMenuToggle.classList.remove('active');
            document.body.classList.remove('mobile-nav-open');
            mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }
    }

    // Mobile menu toggle
    if (mobileMenuToggle && mobileNavOverlay) {
        mobileMenuToggle.addEventListener('click', function() {
            if (mobileNavOverlay.classList.contains('active')) {
                closeMobileNav();
            } else {
                openMobileNav();
            }
        });
    }

    // Mobile nav close button
    if (mobileNavClose) {
        mobileNavClose.addEventListener('click', closeMobileNav);
    }

    // Close mobile nav when clicking overlay
    if (mobileNavOverlay) {
        mobileNavOverlay.addEventListener('click', function(e) {
            if (e.target === mobileNavOverlay) {
                closeMobileNav();
            }
        });
    }

    // Mobile search button
    if (mobileSearchBtn) {
        mobileSearchBtn.addEventListener('click', function() {
            closeMobileNav();
            // Trigger search overlay
            if (searchToggle) {
                searchToggle.click();
            }
        });
    }

    // Mobile theme button
    if (mobileThemeBtn) {
        mobileThemeBtn.addEventListener('click', function() {
            // Trigger theme toggle
            if (themeToggle) {
                themeToggle.click();
            }
            // Update mobile theme button icon
            updateMobileThemeIcon();
        });
    }

    // Update mobile theme icon to match main theme toggle
    function updateMobileThemeIcon() {
        const sunIcon = mobileThemeBtn.querySelector('.sun-icon');
        const moonIcon = mobileThemeBtn.querySelector('.moon-icon');
        const mainSunIcon = themeToggle.querySelector('.sun-icon');
        const mainMoonIcon = themeToggle.querySelector('.moon-icon');

        if (mainSunIcon.style.display === 'none') {
            sunIcon.style.display = 'none';
            moonIcon.style.display = 'block';
        } else {
            sunIcon.style.display = 'block';
            moonIcon.style.display = 'none';
        }
    }

    // Close mobile nav when clicking on nav links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    if (mobileNavLinks.length > 0) {
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMobileNav();

                // Add active state to clicked link
                mobileNavLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }

    // Handle escape key to close mobile nav
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileNavOverlay && mobileNavOverlay.classList.contains('active')) {
            closeMobileNav();
        }
    });

    // Prevent scroll when mobile nav is open
    function preventScroll(e) {
        if (document.body.classList.contains('mobile-nav-open')) {
            e.preventDefault();
        }
    }

    // Add touch event listeners for better mobile experience
    document.addEventListener('touchmove', preventScroll, { passive: false });

    // Touch gesture support for mobile nav
    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;

    // Handle swipe gestures
    function handleSwipeGesture() {
        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;
        const minSwipeDistance = 50;

        // Only handle horizontal swipes that are more significant than vertical ones
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance && mobileNavOverlay) {
            if (deltaX > 0) {
                // Swipe right - open mobile nav (if closed)
                if (!mobileNavOverlay.classList.contains('active')) {
                    openMobileNav();
                }
            } else {
                // Swipe left - close mobile nav (if open)
                if (mobileNavOverlay.classList.contains('active')) {
                    closeMobileNav();
                }
            }
        }
    }

    // Touch event listeners for swipe gestures
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        touchEndY = e.changedTouches[0].screenY;
        handleSwipeGesture();
    }, { passive: true });

    // Initialize mobile nav state on page load
    function initializeMobileNav() {
        // Ensure mobile nav is closed on page load
        closeMobileNav();

        // Update mobile theme icon to match current theme
        if (mobileThemeBtn) {
            updateMobileThemeIcon();
        }

        // Set up intersection observer for nav link active states
        if ('IntersectionObserver' in window) {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.mobile-nav-link[href^="#"]');

            if (sections.length > 0 && navLinks.length > 0) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const id = entry.target.getAttribute('id');
                            navLinks.forEach(link => {
                                link.classList.remove('active');
                                if (link.getAttribute('href') === `#${id}`) {
                                    link.classList.add('active');
                                }
                            });
                        }
                    });
                }, {
                    threshold: 0.3,
                    rootMargin: '-20% 0px -20% 0px'
                });

                sections.forEach(section => observer.observe(section));
            }
        }
    }

    // Call initialization function
    initializeMobileNav();

    // Handle window resize to ensure proper mobile nav behavior
    window.addEventListener('resize', function() {
        // Close mobile nav if window becomes too wide (desktop view)
        if (window.innerWidth > 768 && mobileNavOverlay && mobileNavOverlay.classList.contains('active')) {
            closeMobileNav();
        }
    });

    // Add haptic feedback for supported devices
    function addHapticFeedback() {
        if ('vibrate' in navigator) {
            navigator.vibrate(50); // Short vibration
        }
    }

    // Add haptic feedback to mobile nav interactions
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', addHapticFeedback);
    }

    if (mobileNavClose) {
        mobileNavClose.addEventListener('click', addHapticFeedback);
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function initializeAnimations() {
    // Add scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe tool cards
    document.querySelectorAll('.tool-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

function openConverter(tool) {
    if (!conversionData[tool]) return;

    currentTool = tool;
    const data = conversionData[tool];

    modalTitle.textContent = data.title;

    // Populate unit dropdowns
    populateUnitDropdowns(data.units);

    // Reset values and validation
    fromValue.value = '';
    toValue.value = '';
    clearValidation();
    hideError();
    hideConversionDetails();

    // Show modal
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Focus on input
    setTimeout(() => fromValue.focus(), 100);
}

function populateUnitDropdowns(units) {
    fromUnit.innerHTML = '';
    toUnit.innerHTML = '';
    
    const unitKeys = Object.keys(units);
    
    unitKeys.forEach(key => {
        const option1 = document.createElement('option');
        option1.value = key;
        option1.textContent = units[key].name;
        fromUnit.appendChild(option1);
        
        const option2 = document.createElement('option');
        option2.value = key;
        option2.textContent = units[key].name;
        toUnit.appendChild(option2);
    });
    
    // Set default selections
    if (unitKeys.length > 1) {
        toUnit.selectedIndex = 1;
    }
}

function performConversion() {
    const inputValue = fromValue.value.trim();

    // Clear previous validation
    clearValidation();
    hideError();

    // Check if input is empty
    if (inputValue === '') {
        toValue.value = '';
        hideConversionDetails();
        return;
    }

    const fromVal = parseFloat(inputValue);

    // Validate input
    const validation = validateInput(fromVal, currentTool);
    if (!validation.isValid) {
        showError(validation.message);
        showValidation(validation.message, 'error');
        toValue.value = '';
        hideConversionDetails();
        fromValue.classList.add('error');
        fromValue.classList.remove('success');
        return;
    }

    // Input is valid
    fromValue.classList.add('success');
    fromValue.classList.remove('error');
    showValidation('Valid input', 'success');

    const data = conversionData[currentTool];
    const fromUnitKey = fromUnit.value;
    const toUnitKey = toUnit.value;

    let result;
    let formula = '';

    if (currentTool === 'currency') {
        // Currency conversion using rates
        const fromRate = data.units[fromUnitKey].rate;
        const toRate = data.units[toUnitKey].rate;
        result = (fromVal / fromRate) * toRate;
        formula = `${fromVal} ÷ ${fromRate} × ${toRate}`;
    } else {
        // Standard unit conversion using factors
        const fromFactor = data.units[fromUnitKey].factor;
        const toFactor = data.units[toUnitKey].factor;
        result = (fromVal * fromFactor) / toFactor;
        formula = `${fromVal} × ${fromFactor} ÷ ${toFactor}`;
    }

    // Format result
    const formattedResult = formatResult(result);
    toValue.value = formattedResult;

    // Show conversion info
    showConversionInfo(fromVal, result, data.units[fromUnitKey].name, data.units[toUnitKey].name);

    // Show conversion details
    showConversionDetails(formula, result);
}

function formatResult(value) {
    if (value === 0) return '0';
    
    // For very large or very small numbers, use scientific notation
    if (Math.abs(value) >= 1e15 || (Math.abs(value) < 1e-6 && Math.abs(value) > 0)) {
        return value.toExponential(6);
    }
    
    // For normal numbers, use appropriate decimal places
    if (Math.abs(value) >= 1000000) {
        return value.toLocaleString('en-US', { maximumFractionDigits: 2 });
    } else if (Math.abs(value) >= 1) {
        return value.toLocaleString('en-US', { maximumFractionDigits: 6 });
    } else {
        return value.toLocaleString('en-US', { maximumFractionDigits: 10 });
    }
}

function swapUnits() {
    const fromIndex = fromUnit.selectedIndex;
    const toIndex = toUnit.selectedIndex;

    fromUnit.selectedIndex = toIndex;
    toUnit.selectedIndex = fromIndex;

    // Swap values if both have values
    if (fromValue.value && toValue.value) {
        const tempValue = fromValue.value;
        fromValue.value = toValue.value;
        toValue.value = tempValue;
    }

    // Clear validation and recalculate
    clearValidation();
    hideError();
    performConversion();

    // Add visual feedback
    swapButton.style.transform = 'rotate(180deg) scale(1.1)';
    setTimeout(() => {
        swapButton.style.transform = '';
    }, 300);
}

function closeModal() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    clearValidation();
    hideError();
    hideConversionDetails();
}

// Validation functions
function validateInput(value, tool) {
    const rules = validationRules[tool];

    // Check if value is a number
    if (isNaN(value)) {
        return {
            isValid: false,
            message: 'Please enter a valid number'
        };
    }

    // Check for infinite or very large numbers
    if (!isFinite(value)) {
        return {
            isValid: false,
            message: 'Value must be finite'
        };
    }

    // Check minimum value
    if (value < rules.min) {
        return {
            isValid: false,
            message: `Value must be greater than or equal to ${rules.min}`
        };
    }

    // Check maximum value
    if (value > rules.max) {
        return {
            isValid: false,
            message: `Value must be less than or equal to ${formatResult(rules.max)}`
        };
    }

    // Check for negative values where not appropriate
    if (value < 0 && ['weight', 'volume', 'time', 'storage', 'speed', 'pressure', 'power', 'length', 'area', 'density'].includes(tool)) {
        return {
            isValid: false,
            message: 'Value cannot be negative for this conversion type'
        };
    }

    return {
        isValid: true,
        message: 'Valid input'
    };
}

function showError(message) {
    const errorText = errorMessage.querySelector('.error-text');
    errorText.textContent = message;
    errorMessage.style.display = 'flex';
}

function hideError() {
    errorMessage.style.display = 'none';
}

function showValidation(message, type) {
    fromValidation.textContent = message;
    fromValidation.className = `input-validation validation-${type}`;
}

function clearValidation() {
    fromValidation.textContent = '';
    fromValidation.className = 'input-validation';
    fromValue.classList.remove('error', 'success');
}

function showConversionInfo(fromVal, toVal, fromUnit, toUnit) {
    const ratio = toVal / fromVal;
    conversionInfo.textContent = `1 ${fromUnit} = ${formatResult(ratio)} ${toUnit}`;
}

function showConversionDetails(formula, result) {
    formulaDisplay.textContent = formula;

    // Determine precision
    let precision = 'Standard';
    if (Math.abs(result) >= 1e15 || (Math.abs(result) < 1e-6 && Math.abs(result) > 0)) {
        precision = 'Scientific notation';
    } else if (Math.abs(result) < 1) {
        precision = 'High precision (10 decimals)';
    } else {
        precision = 'Standard precision';
    }

    precisionDisplay.textContent = precision;
    conversionDetails.style.display = 'block';
}

function hideConversionDetails() {
    conversionDetails.style.display = 'none';
}

// Search functionality
function openSearch() {
    if (searchOverlay) {
        searchOverlay.style.display = 'flex';
        searchInput.focus();
        searchInput.value = '';
        searchResults.innerHTML = '';
        showAllTools();
    }
}

function closeSearch() {
    if (searchOverlay) {
        searchOverlay.style.display = 'none';
    }
}

function handleSearch() {
    const query = searchInput.value.toLowerCase().trim();

    if (query === '') {
        showAllTools();
        return;
    }

    const results = [];

    Object.keys(conversionData).forEach(toolKey => {
        const tool = conversionData[toolKey];
        const metadata = toolMetadata[toolKey];

        // Search in title, description, and keywords
        const searchText = [
            tool.title,
            metadata.keywords.join(' ')
        ].join(' ').toLowerCase();

        if (searchText.includes(query)) {
            results.push({
                key: toolKey,
                title: tool.title,
                description: `${Object.keys(tool.units).length} units available`,
                shortcut: metadata.shortcut
            });
        }
    });

    displaySearchResults(results);
}

function showAllTools() {
    const results = Object.keys(conversionData).map(toolKey => {
        const tool = conversionData[toolKey];
        const metadata = toolMetadata[toolKey];

        return {
            key: toolKey,
            title: tool.title,
            description: `${Object.keys(tool.units).length} units available`,
            shortcut: metadata.shortcut
        };
    });

    displaySearchResults(results);
}

function displaySearchResults(results) {
    if (results.length === 0) {
        searchResults.innerHTML = `
            <div class="search-result-item">
                <div class="search-result-content">
                    <div class="search-result-title">No results found</div>
                    <div class="search-result-description">Try a different search term</div>
                </div>
            </div>
        `;
        return;
    }

    searchResults.innerHTML = results.map(result => `
        <div class="search-result-item" data-tool="${result.key}">
            <div class="search-result-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
            </div>
            <div class="search-result-content">
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-description">${result.description}</div>
            </div>
            <div class="search-result-shortcut">${result.shortcut}</div>
        </div>
    `).join('');

    // Add click handlers
    searchResults.querySelectorAll('.search-result-item').forEach(item => {
        item.addEventListener('click', function() {
            const tool = this.getAttribute('data-tool');
            closeSearch();
            openConverter(tool);
        });
    });
}

// Tool filtering
function filterTools(category) {
    const toolCards = document.querySelectorAll('.tool-card');

    toolCards.forEach(card => {
        if (category === 'all') {
            card.classList.remove('hidden');
        } else {
            const cardCategory = card.getAttribute('data-category');
            if (cardCategory === category) {
                card.classList.remove('hidden');
            } else {
                card.classList.add('hidden');
            }
        }
    });
}

// History management
function saveCurrentConversion() {
    if (!fromValue.value || !toValue.value) return;

    const conversion = {
        id: Date.now(),
        tool: currentTool,
        toolTitle: conversionData[currentTool].title,
        fromValue: fromValue.value,
        fromUnit: fromUnit.options[fromUnit.selectedIndex].text,
        toValue: toValue.value,
        toUnit: toUnit.options[toUnit.selectedIndex].text,
        timestamp: new Date().toISOString()
    };

    conversionHistory.unshift(conversion);

    // Keep only last 50 conversions
    if (conversionHistory.length > 50) {
        conversionHistory = conversionHistory.slice(0, 50);
    }

    localStorage.setItem('conversionHistory', JSON.stringify(conversionHistory));

    // Visual feedback
    saveToHistory.classList.add('success');
    saveToHistory.innerHTML = `
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        Saved
    `;

    setTimeout(() => {
        saveToHistory.classList.remove('success');
        saveToHistory.innerHTML = `
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
            </svg>
            Save
        `;
    }, 2000);
}

function copyConversionResult() {
    if (!toValue.value) return;

    const textToCopy = `${fromValue.value} ${fromUnit.options[fromUnit.selectedIndex].text} = ${toValue.value} ${toUnit.options[toUnit.selectedIndex].text}`;

    navigator.clipboard.writeText(textToCopy).then(() => {
        // Visual feedback
        copyResult.classList.add('success');
        copyResult.innerHTML = `
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            Copied
        `;

        setTimeout(() => {
            copyResult.classList.remove('success');
            copyResult.innerHTML = `
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
                Copy
            `;
        }, 2000);
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    });
}

function openHistoryModal() {
    if (historyModal) {
        historyModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        renderHistoryList();
    }
}

function closeHistoryModal() {
    if (historyModal) {
        historyModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function renderHistoryList() {
    if (conversionHistory.length === 0) {
        historyList.innerHTML = `
            <div class="history-empty">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9z"/>
                </svg>
                <p>No conversion history yet</p>
                <span>Your recent conversions will appear here</span>
            </div>
        `;
        return;
    }

    historyList.innerHTML = conversionHistory.map(item => {
        const date = new Date(item.timestamp);
        const timeAgo = getTimeAgo(date);

        return `
            <div class="history-item" data-id="${item.id}">
                <div class="history-conversion">
                    <div class="history-title">${item.toolTitle}</div>
                    <div class="history-details">${item.fromValue} ${item.fromUnit} → ${item.toValue} ${item.toUnit}</div>
                    <div class="history-time">${timeAgo}</div>
                </div>
                <div class="history-actions">
                    <button class="history-action" onclick="replayConversion('${item.tool}', '${item.fromValue}', '${item.fromUnit}', '${item.toUnit}')" title="Replay conversion">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M4 12a8 8 0 018-8V2.5L16 6l-4 3.5V8a6 6 0 100 8h4v2H8a8 8 0 01-4-8z"/>
                        </svg>
                    </button>
                    <button class="history-action" onclick="deleteHistoryItem(${item.id})" title="Delete">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
}

function clearConversionHistory() {
    if (confirm('Are you sure you want to clear all conversion history?')) {
        conversionHistory = [];
        localStorage.removeItem('conversionHistory');
        renderHistoryList();
    }
}

function deleteHistoryItem(id) {
    conversionHistory = conversionHistory.filter(item => item.id !== id);
    localStorage.setItem('conversionHistory', JSON.stringify(conversionHistory));
    renderHistoryList();
}

function replayConversion(tool, fromVal, fromUnitText, toUnitText) {
    closeHistoryModal();
    openConverter(tool);

    setTimeout(() => {
        fromValue.value = fromVal;

        // Find and select the correct units
        const fromOptions = Array.from(fromUnit.options);
        const toOptions = Array.from(toUnit.options);

        const fromIndex = fromOptions.findIndex(option => option.text === fromUnitText);
        const toIndex = toOptions.findIndex(option => option.text === toUnitText);

        if (fromIndex !== -1) fromUnit.selectedIndex = fromIndex;
        if (toIndex !== -1) toUnit.selectedIndex = toIndex;

        performConversion();
    }, 100);
}

// Keyboard shortcuts and accessibility
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Global shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'k':
                case 'K':
                    e.preventDefault();
                    openSearch();
                    break;
                case 't':
                case 'T':
                    e.preventDefault();
                    toggleTheme();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                case '0':
                    e.preventDefault();
                    const toolIndex = e.key === '0' ? 9 : parseInt(e.key) - 1;
                    const tools = Object.keys(conversionData);
                    if (tools[toolIndex]) {
                        openConverter(tools[toolIndex]);
                    }
                    break;
            }

            // Shift combinations
            if (e.shiftKey) {
                switch(e.key) {
                    case '!': // Ctrl+Shift+1
                        e.preventDefault();
                        openConverter('density');
                        break;
                    case '@': // Ctrl+Shift+2
                        e.preventDefault();
                        openConverter('area');
                        break;
                }
            }
        }

        // Modal-specific shortcuts
        if (modal.style.display === 'block') {
            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'Tab' && !e.shiftKey) {
                // Swap units with Tab in converter
                if (document.activeElement === fromValue) {
                    e.preventDefault();
                    swapUnits();
                }
            }
        }

        // Search overlay shortcuts
        if (searchOverlay && searchOverlay.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeSearch();
            } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                e.preventDefault();
                navigateSearchResults(e.key === 'ArrowDown' ? 1 : -1);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                selectCurrentSearchResult();
            }
        }

        // History modal shortcuts
        if (historyModal && historyModal.style.display === 'block') {
            if (e.key === 'Escape') {
                closeHistoryModal();
            }
        }
    });
}

function initializeAccessibility() {
    // Add ARIA labels and roles
    document.querySelectorAll('.tool-card').forEach((card, index) => {
        card.setAttribute('role', 'button');
        card.setAttribute('tabindex', '0');
        card.setAttribute('aria-label', `Open ${card.querySelector('.tool-title').textContent} converter`);

        // Keyboard navigation for tool cards
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Add focus management
    if (modal && fromValue) {
        modal.addEventListener('shown', function() {
            fromValue.focus();
        });
    }

    // Announce theme changes
    const themeAnnouncer = document.createElement('div');
    themeAnnouncer.setAttribute('aria-live', 'polite');
    themeAnnouncer.setAttribute('aria-atomic', 'true');
    themeAnnouncer.style.position = 'absolute';
    themeAnnouncer.style.left = '-10000px';
    themeAnnouncer.style.width = '1px';
    themeAnnouncer.style.height = '1px';
    themeAnnouncer.style.overflow = 'hidden';
    document.body.appendChild(themeAnnouncer);

    // Update announcer on theme change
    const originalToggleTheme = toggleTheme;
    toggleTheme = function() {
        originalToggleTheme();
        themeAnnouncer.textContent = `Switched to ${currentTheme} theme`;
    };
}

let currentSearchResultIndex = -1;

function navigateSearchResults(direction) {
    const results = searchResults.querySelectorAll('.search-result-item');
    if (results.length === 0) return;

    // Remove previous highlight
    if (currentSearchResultIndex >= 0 && currentSearchResultIndex < results.length) {
        results[currentSearchResultIndex].style.background = '';
    }

    // Update index
    currentSearchResultIndex += direction;
    if (currentSearchResultIndex < 0) currentSearchResultIndex = results.length - 1;
    if (currentSearchResultIndex >= results.length) currentSearchResultIndex = 0;

    // Highlight current result
    results[currentSearchResultIndex].style.background = 'rgba(99, 102, 241, 0.2)';
    results[currentSearchResultIndex].scrollIntoView({ block: 'nearest' });
}

function selectCurrentSearchResult() {
    const results = searchResults.querySelectorAll('.search-result-item');
    if (currentSearchResultIndex >= 0 && currentSearchResultIndex < results.length) {
        results[currentSearchResultIndex].click();
    }
}

function showShortcutsHelp() {
    if (shortcutsHelp) {
        shortcutsHelp.style.display = 'block';
    }
}

function hideShortcutsHelp() {
    if (shortcutsHelp) {
        shortcutsHelp.style.display = 'none';
    }
}

// PWA functionality
let deferredPrompt;

function initializePWA() {
    // Register service worker
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);

                    // Check for updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                showUpdateAvailable();
                            }
                        });
                    });
                })
                .catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });
        });
    }

    // Handle install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallPrompt();
    });

    // Handle app installed
    window.addEventListener('appinstalled', () => {
        console.log('PWA was installed');
        hideInstallPrompt();
        deferredPrompt = null;
    });

    // Handle URL parameters for direct tool access
    const urlParams = new URLSearchParams(window.location.search);
    const tool = urlParams.get('tool');
    if (tool && conversionData[tool]) {
        setTimeout(() => openConverter(tool), 500);
    }
}

function showInstallPrompt() {
    const installBanner = document.createElement('div');
    installBanner.id = 'installBanner';
    installBanner.innerHTML = `
        <div class="install-banner">
            <div class="install-content">
                <div class="install-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                    </svg>
                </div>
                <div class="install-text">
                    <div class="install-title">Install Smartnery</div>
                    <div class="install-description">Get quick access and work offline</div>
                </div>
                <div class="install-actions">
                    <button class="install-btn" id="installBtn">Install</button>
                    <button class="install-close" id="installClose">&times;</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(installBanner);

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .install-banner {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            right: 2rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--shadow-xl);
            z-index: 2000;
            animation: slideInUp 0.3s ease;
        }

        .install-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .install-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .install-icon svg {
            width: 20px;
            height: 20px;
        }

        .install-text {
            flex: 1;
        }

        .install-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .install-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .install-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .install-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .install-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .install-close {
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            font-size: 1.5rem;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .install-close:hover {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .install-banner {
                left: 1rem;
                right: 1rem;
                bottom: 1rem;
            }

            .install-content {
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .install-actions {
                width: 100%;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);

    // Add event listeners
    const installBtn = document.getElementById('installBtn');
    const installClose = document.getElementById('installClose');

    if (installBtn) {
        installBtn.addEventListener('click', installApp);
    }
    if (installClose) {
        installClose.addEventListener('click', hideInstallPrompt);
    }

    // Auto-hide after 10 seconds
    setTimeout(hideInstallPrompt, 10000);
}

function hideInstallPrompt() {
    const banner = document.getElementById('installBanner');
    if (banner) {
        banner.remove();
    }
}

function installApp() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
            deferredPrompt = null;
        });
    }
    hideInstallPrompt();
}

function showUpdateAvailable() {
    const updateBanner = document.createElement('div');
    updateBanner.innerHTML = `
        <div class="update-banner">
            <div class="update-content">
                <span>A new version is available!</span>
                <button class="update-btn" onclick="updateApp()">Update</button>
                <button class="update-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        </div>
    `;

    const style = document.createElement('style');
    style.textContent = `
        .update-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--gradient-primary);
            color: white;
            z-index: 3000;
            animation: slideInDown 0.3s ease;
        }

        .update-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            padding: 0.75rem;
        }

        .update-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .update-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.25rem;
            padding: 0.25rem;
        }

        @keyframes slideInDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
    document.body.appendChild(updateBanner);
}

function updateApp() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(registration => {
            if (registration && registration.waiting) {
                registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                window.location.reload();
            }
        });
    }
}

// Add some visual feedback for interactions
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('tool-card') || e.target.closest('.tool-card')) {
        const card = e.target.classList.contains('tool-card') ? e.target : e.target.closest('.tool-card');
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);
    }
});

// ===== ENHANCED FEATURES =====

// 1. Particle Background System
function initializeParticles() {
    const particlesContainer = document.getElementById('particlesContainer');
    if (!particlesContainer) return;

    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        createParticle(particlesContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');
    particle.className = 'particle';

    // Random size between 2-8px
    const size = Math.random() * 6 + 2;
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';

    // Random position
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';

    // Random animation duration
    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
    particle.style.animationDelay = Math.random() * 2 + 's';

    container.appendChild(particle);

    // Remove and recreate particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.remove();
            createParticle(container);
        }
    }, 8000);
}

// 2. Typewriter Effect
function initializeTypewriter() {
    const titleElement = document.getElementById('heroTitle');
    if (!titleElement) return;

    const text = 'Advanced Unit Conversion Tools';
    titleElement.textContent = '';

    let index = 0;
    function typeWriter() {
        if (index < text.length) {
            titleElement.textContent += text.charAt(index);
            index++;
            setTimeout(typeWriter, 100);
        } else {
            // Remove cursor after typing is complete
            setTimeout(() => {
                titleElement.style.borderRight = 'none';
            }, 1000);
        }
    }

    // Start typing after a short delay
    setTimeout(typeWriter, 500);
}

// 3. Animated Counters
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number[data-target]');

    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }

        // Format the number
        if (target === 100) {
            element.textContent = Math.floor(current) + '+';
        } else {
            element.textContent = Math.floor(current);
        }
    }, 16);
}

// 4. Live Converter Preview
function initializeLiveConverter() {
    const liveInput = document.getElementById('liveInput');
    const liveResult = document.getElementById('liveResult');

    if (!liveInput || !liveResult) return;

    function formatNumber(num) {
        if (num === 0) return '0';

        // For very large numbers, use scientific notation
        if (Math.abs(num) >= 1e6) {
            return num.toExponential(2);
        }

        // For normal numbers, limit decimal places and add commas
        if (Math.abs(num) >= 1000) {
            return num.toLocaleString('en-US', {
                maximumFractionDigits: 2,
                minimumFractionDigits: 0
            });
        }

        // For small numbers, show up to 3 decimal places
        return num.toFixed(3).replace(/\.?0+$/, '');
    }

    function updateConversion() {
        const meters = parseFloat(liveInput.value) || 0;
        const feet = meters * 3.28084;
        liveResult.textContent = formatNumber(feet);

        // Add tooltip for exact value if it's formatted
        if (Math.abs(feet) >= 1000 || Math.abs(feet) >= 1e6) {
            liveResult.title = `Exact value: ${feet}`;
        } else {
            liveResult.title = '';
        }
    }

    if (liveInput) {
        liveInput.addEventListener('input', updateConversion);

        // Add some visual effects
        liveInput.addEventListener('focus', () => {
            liveInput.parentElement.style.transform = 'scale(1.02)';
        });

        liveInput.addEventListener('blur', () => {
            liveInput.parentElement.style.transform = 'scale(1)';
        });
    }

    // Initialize with default value
    updateConversion();
}

// 5. Mouse Follower Effect
function initializeMouseFollower() {
    const mouseFollower = document.getElementById('mouseFollower');
    if (!mouseFollower) return;

    let mouseX = 0;
    let mouseY = 0;
    let followerX = 0;
    let followerY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    function animateFollower() {
        // Smooth following with easing
        followerX += (mouseX - followerX) * 0.1;
        followerY += (mouseY - followerY) * 0.1;

        mouseFollower.style.left = followerX - 10 + 'px';
        mouseFollower.style.top = followerY - 10 + 'px';

        requestAnimationFrame(animateFollower);
    }

    animateFollower();

    // Hide on mobile devices
    if (window.innerWidth <= 768) {
        mouseFollower.style.display = 'none';
    }

    // Enhanced interaction effects
    document.addEventListener('mouseenter', (e) => {
        if (e.target.classList.contains('tool-card') ||
            e.target.classList.contains('nav-link') ||
            e.target.tagName === 'BUTTON') {
            mouseFollower.style.transform = 'scale(2)';
            mouseFollower.style.background = 'radial-gradient(circle, rgba(139, 92, 246, 0.8) 0%, transparent 70%)';
        }
    }, true);

    document.addEventListener('mouseleave', (e) => {
        if (e.target.classList.contains('tool-card') ||
            e.target.classList.contains('nav-link') ||
            e.target.tagName === 'BUTTON') {
            mouseFollower.style.transform = 'scale(1)';
            mouseFollower.style.background = 'radial-gradient(circle, rgba(99, 102, 241, 0.6) 0%, transparent 70%)';
        }
    }, true);
}

// Social Media Share Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle share link clicks
    const shareLinks = document.querySelectorAll('.social-icon.link');
    shareLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Copy current page URL to clipboard
            const currentUrl = window.location.href;

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(currentUrl).then(() => {
                    showShareNotification('Link copied to clipboard!');
                }).catch(() => {
                    fallbackCopyTextToClipboard(currentUrl);
                });
            } else {
                fallbackCopyTextToClipboard(currentUrl);
            }
        });
    });
});

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showShareNotification('Link copied to clipboard!');
    } catch (err) {
        showShareNotification('Failed to copy link');
    }

    document.body.removeChild(textArea);
}

function showShareNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
